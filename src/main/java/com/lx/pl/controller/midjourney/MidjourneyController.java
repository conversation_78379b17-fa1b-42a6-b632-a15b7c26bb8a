package com.lx.pl.controller.midjourney;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.lx.pl.annotation.Authorization;
import com.lx.pl.annotation.CurrentUser;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.dto.GenGenericPara;
import com.lx.pl.dto.Resolution;
import com.lx.pl.dto.generic.R;
import com.lx.pl.dto.midjourney.MidjourneyResponse;
import com.lx.pl.enums.LogicErrorCode;
import com.lx.pl.enums.OriginCreate;
import com.lx.pl.enums.VipType;
import com.lx.pl.exception.LogicException;
import com.lx.pl.service.GenService;
import com.lx.pl.service.MidjourneyService;
import com.lx.pl.service.PromptFiltrationService;
import com.lx.pl.service.VipService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.UUID;

import org.springframework.web.multipart.MultipartFile;
import com.lx.pl.util.AspectRatioUtils;
import com.lx.pl.util.ImageBase64Utils;

/**
 * Midjourney API控制器
 *
 * <AUTHOR>
 */
@Slf4j
@Tag(name = "Midjourney API")
@RestController
@RequestMapping("/api/midjourney")
@Validated
public class MidjourneyController {

    @Value("${bad.words.filter}")
    Boolean badWordsFilter;

    @Autowired
    private MidjourneyService midjourneyService;

    @Autowired
    private PromptFiltrationService promptFiltrationService;

    @Autowired
    private VipService vipStandardsService;

    @Autowired
    private GenService genService;

    /**
     * 生成图像请求参数
     */
    @Data
    public static class ImagineParams {
        @NotBlank(message = "提示词不能为空")
        private String prompt;

        private String mode = "relax"; // fast/relax/turbo

        private Boolean enablePromptCheck = true; // 是否启用prompt检查
    }

    /**
     * 操作请求参数
     */
    @Data
    public static class ActionParams {
        @NotBlank(message = "任务ID不能为空")
        private String jobId;

        @NotBlank(message = "操作类型不能为空")
        private String action;
    }

    /**
     * 图像合成请求参数
     */
    @Data
    public static class BlendParams {
        @NotNull(message = "图像列表不能为空")
        private List<String> imageBase64List;

        private String dimensions = "SQUARE"; // PORTRAIT/SQUARE/LANDSCAPE
        private String mode = "fast";
    }

    /**
     * 图像描述请求参数
     */
    @Data
    public static class DescribeParams {
        private String imageBase64;
        private String imageUrl;
        private String mode = "fast";
    }

    /**
     * Prompt检查请求参数
     */
    @Data
    public static class PromptCheckParams {
        @NotBlank(message = "提示词不能为空")
        private String prompt;
    }

    /**
     * 生成图像 - /imagine
     */
    @PostMapping("/imagine")
    @Operation(summary = "生成图像")
    @Authorization
    public R<MidjourneyResponse.BaseResponseData> imagine(
            @Parameter(hidden = true) @CurrentUser User user,
            @RequestBody @Validated ImagineParams params) {

        log.info("Midjourney imagine request from user: {}, prompt: {}, enablePromptCheck: {}",
                user.getLoginName(), params.getPrompt(), params.getEnablePromptCheck());

        // 检查并发任务数限制
        if (midjourneyService.checkMidjourneyConcurrentJobs(user, null)) {
            throw new LogicException(LogicErrorCode.EXCEED_CONCURRENT_JOBS);
        }

        MidjourneyResponse.BaseResponseData result = midjourneyService.imagine(
                params.getPrompt(),
                params.getMode(),
                user,
                params.getEnablePromptCheck() != null ? params.getEnablePromptCheck() : true
        );

        return R.success(result);
    }

    /**
     * 执行操作 - U1~U4、V1~V4等
     */
    @PostMapping("/action")
    @Operation(summary = "执行操作")
    @Authorization
    public R<MidjourneyResponse.BaseResponseData> action(
            @Parameter(hidden = true) @CurrentUser User user,
            @RequestBody @Validated ActionParams params) {

        log.info("Midjourney action request from user: {}, jobId: {}, action: {}",
                user.getLoginName(), params.getJobId(), params.getAction());

        // 检查并发任务数限制
        if (midjourneyService.checkMidjourneyConcurrentJobs(user, null)) {
            throw new LogicException(LogicErrorCode.EXCEED_CONCURRENT_JOBS);
        }

        MidjourneyResponse.BaseResponseData result = midjourneyService.action(
                params.getJobId(),
                params.getAction(),
                user
        );

        return R.success(result);
    }

    /**
     * 图像合成 - /blend
     */
    @PostMapping("/blend")
    @Operation(summary = "图像合成")
    @Authorization
    public R<MidjourneyResponse.BaseResponseData> blend(
            @Parameter(hidden = true) @CurrentUser User user,
            @RequestBody @Validated BlendParams params) {

        log.info("Midjourney blend request from user: {}, images count: {}",
                user.getLoginName(), params.getImageBase64List().size());

        // 检查并发任务数限制
        if (midjourneyService.checkMidjourneyConcurrentJobs(user, null)) {
            throw new LogicException(LogicErrorCode.EXCEED_CONCURRENT_JOBS);
        }

        MidjourneyResponse.BaseResponseData result = midjourneyService.blend(
                params.getImageBase64List(),
                params.getDimensions(),
                params.getMode(),
                user
        );

        return R.success(result);
    }

    /**
     * 图像描述 - /describe
     */
    @PostMapping("/describe")
    @Operation(summary = "图像描述")
    @Authorization
    public R<MidjourneyResponse.BaseResponseData> describe(
            @Parameter(hidden = true) @CurrentUser User user,
            @RequestBody @Validated DescribeParams params) {

        log.info("Midjourney describe request from user: {}", user.getLoginName());

        if (StringUtil.isBlank(params.getImageBase64()) && StringUtil.isBlank(params.getImageUrl())) {
            throw new LogicException(LogicErrorCode.ILLEGAL_REQUEST);
        }

        // 检查并发任务数限制
        if (midjourneyService.checkMidjourneyConcurrentJobs(user, null)) {
            throw new LogicException(LogicErrorCode.EXCEED_CONCURRENT_JOBS);
        }

        MidjourneyResponse.BaseResponseData result = midjourneyService.describe(
                params.getImageBase64(),
                params.getImageUrl(),
                params.getMode(),
                user
        );

        return R.success(result);
    }

    /**
     * 查询任务状态
     */
    @GetMapping("/status/{jobId}")
    @Operation(summary = "查询任务状态")
    @Authorization
    public R<MidjourneyResponse.TaskStatusResponse> getTaskStatus(
            @Parameter(hidden = true) @CurrentUser User user,
            @PathVariable @NotBlank(message = "任务ID不能为空") String jobId) {

        log.info("Midjourney status request from user: {}, jobId: {}", user.getLoginName(), jobId);

        MidjourneyResponse.TaskStatusResponse result = midjourneyService.getTaskStatus(jobId);

        return R.success(result);
    }

    /**
     * 获取种子值
     */
    @PostMapping("/seed")
    @Operation(summary = "获取种子值")
    @Authorization
    public R<MidjourneyResponse.BaseResponseData> getSeed(
            @Parameter(hidden = true) @CurrentUser User user,
            @RequestBody @Validated ActionParams params) {

        log.info("Midjourney seed request from user: {}, jobId: {}", user.getLoginName(), params.getJobId());

        // 检查并发任务数限制
        if (midjourneyService.checkMidjourneyConcurrentJobs(user, null)) {
            throw new LogicException(LogicErrorCode.EXCEED_CONCURRENT_JOBS);
        }

        // 种子操作使用特殊的action类型
        MidjourneyResponse.BaseResponseData result = midjourneyService.action(
                params.getJobId(),
                "seed",
                user
        );

        return R.success(result);
    }

    /**
     * 文件上传并转换为Base64（用于blend和describe）
     */
    @PostMapping("/upload-image")
    @Operation(summary = "上传图片并转换为Base64")
    @Authorization
    public R<String> uploadImage(
            @Parameter(hidden = true) @CurrentUser User user,
            @RequestParam("file") MultipartFile file) {

        log.info("Upload image request from user: {}, filename: {}", user.getLoginName(), file.getOriginalFilename());

        // 验证文件类型
        String contentType = file.getContentType();
        if (contentType == null || !contentType.startsWith("image/")) {
            return R.fail(400, "只支持图片文件");
        }

        // 验证文件大小（5MB限制）
        if (file.getSize() > 5 * 1024 * 1024) {
            return R.fail(400, "图片大小不能超过5MB");
        }

        try {
            String base64 = ImageBase64Utils.fileToBase64(file);
            return R.success(base64);
        } catch (Exception e) {
            log.error("Upload image error", e);
            return R.fail(400, "图片上传失败");
        }
    }

    /**
     * 通过文件上传进行图像合成
     */
    @PostMapping("/blend-files")
    @Operation(summary = "通过文件上传进行图像合成")
    @Authorization
    public R<MidjourneyResponse.BaseResponseData> blendFiles(
            @Parameter(hidden = true) @CurrentUser User user,
            @RequestParam("files") List<MultipartFile> files,
            @RequestParam(value = "dimensions", defaultValue = "SQUARE") String dimensions,
            @RequestParam(value = "mode", defaultValue = "fast") String mode) {

        log.info("Midjourney blend files request from user: {}, files count: {}", user.getLoginName(), files.size());

        if (files.size() < 2 || files.size() > 5) {
            return R.fail(400, "图片数量必须在2-5张之间");
        }

        // 检查并发任务数限制
        if (midjourneyService.checkMidjourneyConcurrentJobs(user, null)) {
            throw new LogicException(LogicErrorCode.EXCEED_CONCURRENT_JOBS);
        }

        try {
            List<String> imageBase64List = new ArrayList<>();
            for (MultipartFile file : files) {
                // 验证文件类型
                String contentType = file.getContentType();
                if (contentType == null || !contentType.startsWith("image/")) {
                    return R.fail(400, "只支持图片文件");
                }

                // 验证文件大小
                if (file.getSize() > 5 * 1024 * 1024) {
                    return R.fail(400, "图片大小不能超过5MB");
                }

                String base64 = ImageBase64Utils.fileToBase64(file);
                imageBase64List.add(base64);
            }

            MidjourneyResponse.BaseResponseData result = midjourneyService.blend(
                    imageBase64List,
                    dimensions,
                    mode,
                    user
            );

            return R.success(result);

        } catch (Exception e) {
            log.error("Blend files error", e);
            return R.fail(400, "图像合成失败: " + e.getMessage());
        }
    }

    /**
     * 通过文件上传进行图像描述
     */
    @PostMapping("/describe-file")
    @Operation(summary = "通过文件上传进行图像描述")
    @Authorization
    public R<MidjourneyResponse.BaseResponseData> describeFile(
            @Parameter(hidden = true) @CurrentUser User user,
            @RequestParam("file") MultipartFile file,
            @RequestParam(value = "mode", defaultValue = "fast") String mode) {

        log.info("Midjourney describe file request from user: {}, filename: {}", user.getLoginName(), file.getOriginalFilename());

        // 验证文件类型
        String contentType = file.getContentType();
        if (contentType == null || !contentType.startsWith("image/")) {
            return R.fail(400, "只支持图片文件");
        }

        // 验证文件大小
        if (file.getSize() > 5 * 1024 * 1024) {
            return R.fail(400, "图片大小不能超过5MB");
        }

        // 检查并发任务数限制
        if (midjourneyService.checkMidjourneyConcurrentJobs(user, null)) {
            throw new LogicException(LogicErrorCode.EXCEED_CONCURRENT_JOBS);
        }

        try {
            String base64 = ImageBase64Utils.fileToBase64(file);

            MidjourneyResponse.BaseResponseData result = midjourneyService.describe(
                    base64,
                    null,
                    mode,
                    user
            );

            return R.success(result);

        } catch (Exception e) {
            log.error("Describe file error", e);
            return R.fail(400, "图像描述失败: " + e.getMessage());
        }
    }

    /**
     * Prompt效验 - 检查prompt是否包含被禁止的词汇
     */
    @PostMapping("/prompt-check")
    @Operation(summary = "Prompt效验")
    @Authorization
    public R<MidjourneyResponse.PromptCheckResponse> promptCheck(
            @Parameter(hidden = true) @CurrentUser User user,
            @RequestBody @Validated PromptCheckParams params) {

        log.info("Midjourney prompt check request from user: {}, prompt: {}", user.getLoginName(), params.getPrompt());

        MidjourneyResponse.PromptCheckResponse result = midjourneyService.promptCheck(params.getPrompt());

        return R.success(result);
    }

    // ==================== 兼容原GenController接口 ====================

    /**
     * 兼容原GenController的create接口
     * 接收GenGenericPara格式的请求，转换为Midjourney API调用
     */
    @PostMapping("/create")
    @Operation(summary = "图片生成（兼容原接口）")
    @Authorization
    public R<Map<String, Object>> createCompatible(
            @Parameter(hidden = true) @CurrentUser User user,
            @RequestBody GenGenericPara genParameters,
            HttpServletRequest request) throws IOException {

        log.info("Midjourney compatible create request from user: {}, prompt: {}",
                user.getLoginName(), genParameters.getPrompt());

        // 基础参数验证
        if (genParameters.getPrompt() == null) {
            genParameters.setPrompt("");
        }

        // Prompt过滤验证
        Boolean filterChildSex = promptFiltrationService.filterChildSex(genParameters.getPrompt());
        if (filterChildSex) {
            throw new LogicException(LogicErrorCode.ILLEGAL_PROMPT);
        }

        // 平台验证
        String platform = genService.getPlatform(request);
        if (StringUtil.isBlank(platform)) {
            throw new LogicException(LogicErrorCode.ILLEGAL_REQUEST);
        }

        // 是否开启badword过滤
        if (platform.equals("android") && badWordsFilter) {
            Boolean filterBadWords = promptFiltrationService.filterBadWords(genParameters.getPrompt());
            if (filterBadWords) {
                throw new LogicException(LogicErrorCode.ILLEGAL_PROMPT);
            }
        }

        if (null == genParameters.getNegative_prompt()) {
            genParameters.setNegative_prompt("");
        }
        if (genParameters.getPrompt().length() > 3000) {
            return R.fail(400, "Image prompt exceeds 3000!");
        }
        if (null == genParameters.getResolution()) {
            return R.fail(400, "Image dimensions required !");
        }
        if (0 >= genParameters.getResolution().getWidth()) {
            return R.fail(400, "Image width required !");
        }
        if (0 >= genParameters.getResolution().getHeight()) {
            return R.fail(400, "Image height required !");
        }
        if (2048 < genParameters.getResolution().getWidth()) {
            return R.fail(400, "Image width exceeds 2048 pixels !");
        }
        if (2048 < genParameters.getResolution().getHeight()) {
            return R.fail(400, "Image height exceeds 2048 pixels !");
        }
        if (1 > genParameters.getResolution().getBatch_size()) {
            return R.fail(400, "At least one image required !");
        }

        if (!genService.checkPlatformModel(genParameters.getModel_id(), request)) {
            return R.fail(400, "Model not support !");
        }

        // 判断是否是普通用户
        Boolean notVip = VipType.basic.getValue().equals(user.getVipType());

        // 判断是否fast模式 - 对于Midjourney，我们使用默认的模型ID
//        Boolean fastHour = vipStandardsService.judgeUserFastCreate("midjourney", user, 1, Boolean.FALSE, OriginCreate.create, null, null);
        Boolean fastHour = true;

        //检查权限
        if (notVip) {
            throw new LogicException(LogicErrorCode.NOT_ENOUGH_LUMENS_SUPPORT);
        }

        // 设置传参
        String markId = UUID.randomUUID().toString();
        if (genService.checkUserConcurrentJobs(user, markId)) {
            throw new LogicException(LogicErrorCode.EXCEED_CONCURRENT_JOBS);
        }

        // 构建Midjourney prompt
        String midjourneyPrompt = buildMidjourneyPrompt(genParameters);

        // 确定生成模式
//        String mode = determineMidjourneyMode(genParameters);
        String mode = "fast";

        // 是否启用prompt检查（默认启用，除非明确指定不检查）
        boolean enablePromptCheck = genParameters.getContinueCreate() == null || !genParameters.getContinueCreate();

        log.info("Converted to Midjourney params - prompt: {}, mode: {}, enablePromptCheck: {}",
                midjourneyPrompt, mode, enablePromptCheck);

        // 调用Midjourney服务
        MidjourneyResponse.BaseResponseData result = midjourneyService.imagine(
                midjourneyPrompt,
                mode,
                user,
                enablePromptCheck,
                markId,
                fastHour,
                platform,
                genParameters
        );

        // 构建兼容的响应格式
        Map<String, Object> response = new HashMap<>();
        response.put("promptId", result.getJobId()); // 使用jobId作为promptId
        response.put("taskId", result.getJobId());
        response.put("markId", markId);
        response.put("message", "Task submitted successfully via Midjourney API");
        response.put("provider", "midjourney");
        response.put("fastHour", fastHour);
        response.put("relaxWaitTime", 0); // Midjourney没有relax等待时间概念

        return R.success(response);
    }

    /**
     * 构建Midjourney格式的prompt
     */
    private String buildMidjourneyPrompt(GenGenericPara genParameters) {
        StringBuilder promptBuilder = new StringBuilder();

        // 基础prompt
        if (StringUtil.isNotBlank(genParameters.getPrompt())) {
            promptBuilder.append(genParameters.getPrompt());
        }

        // 添加分辨率参数
        if (genParameters.getResolution() != null) {
            Resolution resolution = genParameters.getResolution();
            if (resolution.getWidth() > 0 && resolution.getHeight() > 0) {
                // 计算宽高比
                String aspectRatio = calculateAspectRatio(resolution.getWidth(), resolution.getHeight());
                if (StringUtil.isNotBlank(aspectRatio)) {
                    promptBuilder.append(" --ar ").append(aspectRatio);
                }
            }
        }

        // 添加负面提示词（Midjourney使用--no参数）
        if (StringUtil.isNotBlank(genParameters.getNegative_prompt())) {
            promptBuilder.append(" --no ").append(genParameters.getNegative_prompt());
        }

//        // 添加种子值
//        if (genParameters.getSeed() != null && genParameters.getSeed() > 0) {
//            promptBuilder.append(" --seed ").append(genParameters.getSeed());
//        }

//        // 添加风格化参数（基于cfg值）
//        if (genParameters.getCfg() != null && genParameters.getCfg() > 0) {
//            // 将CFG值转换为Midjourney的stylize参数
//            int stylize = convertCfgToStylize(genParameters.getCfg());
//            promptBuilder.append(" --stylize ").append(stylize);
//        }

        return promptBuilder.toString().trim();
    }

    /**
     * 计算宽高比
     * 使用AspectRatioUtils获得与前端SHAPE_ALL配置一致的宽高比标签
     */
    private String calculateAspectRatio(int width, int height) {
        return AspectRatioUtils.getAspectRatioLabel(width, height);
    }


    /**
     * 将CFG值转换为Midjourney的stylize参数
     */
    private int convertCfgToStylize(double cfg) {
        // CFG范围通常是1-30，Midjourney stylize范围是0-1000
        // 进行简单的线性映射
        if (cfg <= 1) return 0;
        if (cfg >= 30) return 1000;

        return (int) ((cfg - 1) / 29 * 1000);
    }

    /**
     * 确定Midjourney生成模式
     */
    private String determineMidjourneyMode(GenGenericPara genParameters) {
        // 根据gen_mode参数确定模式
        if (StringUtil.isNotBlank(genParameters.getGen_mode())) {
            switch (genParameters.getGen_mode().toLowerCase()) {
                case "fast":
                    return "fast";
                case "quality":
                    return "relax"; // 高质量模式映射到relax
                default:
                    return "fast";
            }
        }

        // 默认使用fast模式
        return "fast";
    }
}
