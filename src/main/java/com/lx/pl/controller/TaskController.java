package com.lx.pl.controller;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.lx.pl.annotation.Authorization;
import com.lx.pl.annotation.CurrentUser;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.db.mysql.gen.entity.UserTaskQueue;
import com.lx.pl.db.mysql.gen.entity.VipStandards;
import com.lx.pl.dto.GenGenericPara;
import com.lx.pl.dto.ImgDelete;
import com.lx.pl.dto.TaskProcessMessage;
import com.lx.pl.dto.TaskQueueParams;
import com.lx.pl.dto.generic.R;
import com.lx.pl.service.GenService;
import com.lx.pl.enums.LogicErrorCode;
import com.lx.pl.enums.VipType;
import com.lx.pl.exception.LogicException;
import com.lx.pl.service.GenService;
import com.lx.pl.service.TaskService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Tag(name = "任务相关接口")
@Slf4j
@RestController
@RequestMapping("/api/task")
public class TaskController {

    @Autowired
    TaskService taskService;

    @Autowired
    GenService genService;

    @Operation(summary = "取消任务")
    @PostMapping("/cancelTask")
    @Authorization
    public R<Boolean> cancelTask(
            @RequestParam("markId") String markId,
            @CurrentUser @Parameter(hidden = true) User user) {
        Boolean result = taskService.cancelTask(markId, user);
        return result ? R.success(result) : R.fail(1, "Failed");
    }


    @Operation(summary = "排队任务流程")
    @PostMapping("/processTask")
    @Authorization
    public R<TaskProcessMessage> processTask(
            @RequestParam("markId") String markId, HttpServletRequest request) throws JsonProcessingException {
        return R.success(taskService.processTask(markId));
    }


    @Operation(summary = "批量排队任务流程")
    @PostMapping("/batch-process-task")
    @Authorization
    public R<List<TaskProcessMessage>> batchProcessTask(
            @RequestBody List<String> markIdList, HttpServletRequest request) throws JsonProcessingException {
        return R.success(taskService.batchProcessTask(markIdList));
    }

    @Operation(summary = "获取用户预载任务")
    @GetMapping("/select-task-queue")
    @Authorization
    public R<List<UserTaskQueue>> getUserTaskQueue(
            @CurrentUser @Parameter(hidden = true) User user) {
        return R.success(taskService.getUserTaskQueue(user));
    }

    @Operation(summary = "新增用户预载任务")
    @PostMapping("/add-task-queue")
    @Authorization
    public R<String> addUserTaskQueue(@RequestBody TaskQueueParams taskQueueParams,
                                      @CurrentUser @Parameter(hidden = true) User user) {
        return R.success(taskService.addUserTaskQueue(taskQueueParams, user));
    }

    @Operation(summary = "删除用户预载任务")
    @PostMapping("/delete-task-queue")
    @Authorization
    public R<Boolean> deleteUserTaskQueue(@RequestBody List<String> taskQueueIds,
                                          @CurrentUser @Parameter(hidden = true) User user) {
        Boolean result = taskService.deleteUserTaskQueue(taskQueueIds, user);
        return result ? R.success(result) : R.fail(1, "Failed");
    }

    @Operation(summary = "预载任务生图调用")
    @PostMapping(value = "/execute-task")
    @Authorization
    public R<Map<String, Object>> executeTask(@RequestParam("preloadId") String preloadId,
                                              @CurrentUser @Parameter(hidden = true) User user, HttpServletRequest request)
            throws IOException {

        if (StringUtil.isBlank(preloadId)) {
            return R.fail(400, "Parameter cannot be empty !");
        }

//        //普通用户暂时不支持预载
//        if (VipType.basic.getValue().equals(user.getVipType())) {
//            throw new LogicException(LogicErrorCode.NOT_VIP);
//        }

        UserTaskQueue userTaskQueue = taskService.getUserTaskAndDelete(preloadId, user);

        if (Objects.isNull(userTaskQueue)) {
            throw new LogicException(LogicErrorCode.NOT_TASK_EXIST);
        }


        return taskService.executeTask(preloadId, userTaskQueue, user, request);
    }
}
