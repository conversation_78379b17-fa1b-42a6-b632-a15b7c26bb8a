package com.lx.pl.service;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.lx.pl.client.MidjourneyApiClient;
import com.lx.pl.config.MidjourneyConfig;
import com.lx.pl.db.mysql.gen.entity.PromptFile;
import com.lx.pl.db.mysql.gen.entity.PromptRecord;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.db.mysql.gen.mapper.PromptFileMapper;
import com.lx.pl.db.mysql.gen.mapper.PromptRecordMapper;
import com.lx.pl.db.mysql.gen.mapper.UserMapper;
import com.lx.pl.dto.GenGenericPara;
import com.lx.pl.dto.midjourney.MidjourneyRequest;
import com.lx.pl.dto.midjourney.MidjourneyResponse;
import com.lx.pl.enums.LogicErrorCode;
import com.lx.pl.enums.MidjourneyActionType;
import com.lx.pl.enums.MidjourneyTaskStatus;
import com.lx.pl.enums.OriginCreate;
import com.lx.pl.exception.LogicException;
import com.lx.pl.exception.ServerInternalException;
import com.lx.pl.constant.LockPrefixConstant;
import com.lx.pl.util.AspectRatioUtils;
import com.lx.pl.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import retrofit2.Call;
import retrofit2.Response;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * Midjourney服务类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class MidjourneyService {

    @Autowired
    private MidjourneyApiClient midjourneyApiClient;

    @Autowired
    private MidjourneyConfig midjourneyConfig;

    @Autowired
    private RedisService redisService;

    @Autowired
    private PromptRecordMapper promptRecordMapper;

    @Autowired
    private PromptFileMapper promptFileMapper;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private VipService vipService;

    @Autowired
    private RedissonClient redissonClient;

    private static final String MIDJOURNEY_TASK_PREFIX = "midjourney:task:";
    private static final String MIDJOURNEY_USER_TASK_PREFIX = "midjourney:user:";
    private static final String MIDJOURNEY_USER_CONCURRENT_PREFIX = "midjourney:concurrent:";
    private static final String USER_TODAY_CREATE_IMG_NUMS = "user_today_create_img_nums";

    /**
     * 生成图像 - /imagine
     */
    public MidjourneyResponse.BaseResponseData imagine(String prompt, String mode, User user) {
        return imagine(prompt, mode, user, true);
    }

    /**
     * 生成图像 - /imagine
     *
     * @param prompt            提示词
     * @param mode              模式
     * @param user              用户
     * @param enablePromptCheck 是否启用prompt检查
     */
    public MidjourneyResponse.BaseResponseData imagine(String prompt, String mode, User user, boolean enablePromptCheck) {
        return imagine(prompt, mode, user, enablePromptCheck, null, null, null, null);
    }

    /**
     * 生成图像 - /imagine (完整版本，支持PromptRecord入库)
     *
     * @param prompt            提示词
     * @param mode              模式
     * @param user              用户
     * @param enablePromptCheck 是否启用prompt检查
     * @param markId            标记ID
     * @param fastHour          是否快速生图
     * @param platform          平台
     * @param genParameters     原始生图参数（用于记录）
     */
    public MidjourneyResponse.BaseResponseData imagine(String prompt, String mode, User user, boolean enablePromptCheck,
                                                       String markId, Boolean fastHour, String platform, GenGenericPara genParameters) {
        try {
            // 预检查prompt（如果启用且全局配置允许）
            if (enablePromptCheck && midjourneyConfig.getPromptCheckEnabled()) {
                try {
                    log.info("开始Prompt预检查，prompt: {}", prompt);
                    MidjourneyResponse.PromptCheckResponse checkResponse = promptCheck(prompt);

                    if (!"SUCCESS".equals(checkResponse.getStatus())) {
                        log.warn("Prompt检查失败: {}", checkResponse.getMessage());
                        throw new LogicException(LogicErrorCode.MIDJOURNEY_PROMPT_CHECK_FAILED);
                    }

                    log.info("Prompt检查通过，继续生成图像");
                } catch (LogicException e) {
                    // Prompt检查失败，直接抛出，不继续生成
                    log.error("Prompt检查失败，停止生成图像: {}", e.getMessage());
                    throw e;
                } catch (Exception e) {
                    // Prompt检查服务异常，记录日志但不阻止生成（降级处理）
                    log.warn("Prompt检查服务异常，跳过检查继续生成: {}", e.getMessage());
                }
            }

            // 构建请求
            MidjourneyRequest.ImagineRequest request = new MidjourneyRequest.ImagineRequest();
            request.setPrompt(prompt);
            request.setMode(StringUtil.isNotBlank(mode) ? mode : midjourneyConfig.getDefaultMode());
            request.setHookUrl(midjourneyConfig.getCallbackUrl());
            request.setTimeout(midjourneyConfig.getDefaultTimeout());
            request.setTranslation(midjourneyConfig.getTranslationEnabled());
            request.setGetUImages(Boolean.TRUE);

            log.info("Midjourney imagine request: {}", JsonUtils.writeToString(request));

            // 调用API
            Call<MidjourneyResponse> call = midjourneyApiClient.imagine(midjourneyConfig.getApiKey(), request);
            Response<MidjourneyResponse> response = call.execute();

            if (!response.isSuccessful()) {
                log.error("Midjourney imagine API call failed: {}", response.errorBody().string());
                throw new LogicException(LogicErrorCode.MIDJOURNEY_API_ERROR);
            }

            MidjourneyResponse midjourneyResponse = response.body();
            if (!"SUCCESS".equals(midjourneyResponse.getStatus())) {
                log.error("Midjourney imagine failed: {}", midjourneyResponse.getMessage());
                throw new LogicException(LogicErrorCode.MIDJOURNEY_GENERATION_FAILED);
            }

            // 解析响应数据
            MidjourneyResponse.BaseResponseData responseData = JsonUtils.convertValue(
                    midjourneyResponse.getData(), MidjourneyResponse.BaseResponseData.class);

            // 保存任务信息到Redis
            saveTaskToRedis(responseData.getJobId(), user.getLoginName(), "imagine", prompt);

            // 添加到并发任务列表
            String userConcurrentKey = MIDJOURNEY_USER_CONCURRENT_PREFIX + user.getLoginName();
            redisService.putDataToHash(userConcurrentKey, responseData.getJobId(), System.currentTimeMillis(), 2, TimeUnit.HOURS);

            // 如果提供了markId等参数，则保存PromptRecord到数据库
            if (StringUtil.isNotBlank(markId)) {
                savePromptRecord(responseData.getJobId(), prompt, mode, user, markId, fastHour, platform, genParameters);

                // 启动任务状态轮询
                startTaskStatusPolling(responseData.getJobId(), user.getLoginName());
            }

            log.info("Midjourney imagine success, jobId: {}", responseData.getJobId());
            return responseData;

        } catch (IOException e) {
            log.error("Midjourney imagine error", e);
            throw new LogicException(LogicErrorCode.MIDJOURNEY_API_ERROR);
        } catch (LogicException e) {
            // 重新抛出业务异常，保留原始错误信息
            throw e;
        } catch (Exception e) {
            log.error("Midjourney imagine error", e);
            throw new LogicException(LogicErrorCode.MIDJOURNEY_GENERATION_FAILED);
        }
    }

    /**
     * 执行操作 - U1~U4、V1~V4等
     */
    public MidjourneyResponse.BaseResponseData action(String jobId, String actionType, User user) {
        try {
            // 验证操作类型
            MidjourneyActionType.fromAction(actionType);

            // 构建请求
            MidjourneyRequest.ActionRequest request = new MidjourneyRequest.ActionRequest();
            request.setJobId(jobId);
            request.setAction(actionType);
            request.setHookUrl(midjourneyConfig.getCallbackUrl());
            request.setTimeout(midjourneyConfig.getDefaultTimeout());

            log.info("Midjourney action request: {}", JsonUtils.writeToString(request));

            // 调用API
            Call<MidjourneyResponse> call = midjourneyApiClient.action(midjourneyConfig.getApiKey(), request);
            Response<MidjourneyResponse> response = call.execute();

            if (!response.isSuccessful()) {
                log.error("Midjourney action API call failed: {}", response.errorBody().string());
                throw new LogicException(LogicErrorCode.MIDJOURNEY_API_ERROR);
            }

            MidjourneyResponse midjourneyResponse = response.body();
            if (!"SUCCESS".equals(midjourneyResponse.getStatus())) {
                log.error("Midjourney action failed: {}", midjourneyResponse.getMessage());
                throw new LogicException(LogicErrorCode.MIDJOURNEY_ACTION_FAILED);
            }

            // 解析响应数据
            MidjourneyResponse.BaseResponseData responseData = JsonUtils.convertValue(
                    midjourneyResponse.getData(), MidjourneyResponse.BaseResponseData.class);

            // 保存任务信息到Redis
            saveTaskToRedis(responseData.getJobId(), user.getLoginName(), actionType, jobId);

            // 添加到并发任务列表
            String userConcurrentKey = MIDJOURNEY_USER_CONCURRENT_PREFIX + user.getLoginName();
            redisService.putDataToHash(userConcurrentKey, responseData.getJobId(), System.currentTimeMillis(), 2, TimeUnit.HOURS);

            log.info("Midjourney action success, jobId: {}", responseData.getJobId());
            return responseData;

        } catch (IOException e) {
            log.error("Midjourney action error", e);
            throw new LogicException(LogicErrorCode.MIDJOURNEY_API_ERROR);
        } catch (Exception e) {
            log.error("Midjourney action error", e);
            throw new LogicException(LogicErrorCode.MIDJOURNEY_API_ERROR);
        }
    }

    /**
     * 图像合成 - /blend
     */
    public MidjourneyResponse.BaseResponseData blend(List<String> imageBase64List, String dimensions, String mode, User user) {
        try {
            if (imageBase64List == null || imageBase64List.size() < 2 || imageBase64List.size() > 5) {
                throw new LogicException(LogicErrorCode.ILLEGAL_REQUEST);
            }

            // 构建请求
            MidjourneyRequest.BlendRequest request = new MidjourneyRequest.BlendRequest();
            request.setImgBase64Array(imageBase64List);
            request.setDimensions(StringUtil.isNotBlank(dimensions) ? dimensions : "SQUARE");
            request.setMode(StringUtil.isNotBlank(mode) ? mode : midjourneyConfig.getDefaultMode());
            request.setHookUrl(midjourneyConfig.getCallbackUrl());
            request.setTimeout(midjourneyConfig.getDefaultTimeout());

            log.info("Midjourney blend request: {}", JsonUtils.writeToString(request));

            // 调用API
            Call<MidjourneyResponse> call = midjourneyApiClient.blend(midjourneyConfig.getApiKey(), request);
            Response<MidjourneyResponse> response = call.execute();

            if (!response.isSuccessful()) {
                log.error("Midjourney blend API call failed: {}", response.errorBody().string());
                throw new LogicException(LogicErrorCode.MIDJOURNEY_API_ERROR);
            }

            MidjourneyResponse midjourneyResponse = response.body();
            if (!"SUCCESS".equals(midjourneyResponse.getStatus())) {
                log.error("Midjourney blend failed: {}", midjourneyResponse.getMessage());
                throw new LogicException(LogicErrorCode.MIDJOURNEY_ACTION_FAILED);
            }

            // 解析响应数据
            MidjourneyResponse.BaseResponseData responseData = JsonUtils.convertValue(
                    midjourneyResponse.getData(), MidjourneyResponse.BaseResponseData.class);

            // 保存任务信息到Redis
            saveTaskToRedis(responseData.getJobId(), user.getLoginName(), "blend", "blend_images");

            log.info("Midjourney blend success, jobId: {}", responseData.getJobId());
            return responseData;

        } catch (IOException e) {
            log.error("Midjourney blend error", e);
            throw new LogicException(LogicErrorCode.MIDJOURNEY_API_ERROR);
        } catch (Exception e) {
            log.error("Midjourney blend error", e);
            throw new LogicException(LogicErrorCode.MIDJOURNEY_API_ERROR);
        }
    }

    /**
     * 图像描述 - /describe
     */
    public MidjourneyResponse.BaseResponseData describe(String imageBase64, String imageUrl, String mode, User user) {
        try {
            if (StringUtil.isBlank(imageBase64) && StringUtil.isBlank(imageUrl)) {
                throw new LogicException(LogicErrorCode.ILLEGAL_REQUEST);
            }

            // 构建请求
            MidjourneyRequest.DescribeRequest request = new MidjourneyRequest.DescribeRequest();
            request.setBase64(imageBase64);
            request.setUrl(imageUrl);
            request.setMode(StringUtil.isNotBlank(mode) ? mode : midjourneyConfig.getDefaultMode());
            request.setHookUrl(midjourneyConfig.getCallbackUrl());
            request.setTimeout(midjourneyConfig.getDefaultTimeout());

            log.info("Midjourney describe request: {}", JsonUtils.writeToString(request));

            // 调用API
            Call<MidjourneyResponse> call = midjourneyApiClient.describe(midjourneyConfig.getApiKey(), request);
            Response<MidjourneyResponse> response = call.execute();

            if (!response.isSuccessful()) {
                log.error("Midjourney describe API call failed: {}", response.errorBody().string());
                throw new LogicException(LogicErrorCode.MIDJOURNEY_API_ERROR);
            }

            MidjourneyResponse midjourneyResponse = response.body();
            if (!"SUCCESS".equals(midjourneyResponse.getStatus())) {
                log.error("Midjourney describe failed: {}", midjourneyResponse.getMessage());
                throw new LogicException(LogicErrorCode.MIDJOURNEY_ACTION_FAILED);
            }

            // 解析响应数据
            MidjourneyResponse.BaseResponseData responseData = JsonUtils.convertValue(
                    midjourneyResponse.getData(), MidjourneyResponse.BaseResponseData.class);

            // 保存任务信息到Redis
            saveTaskToRedis(responseData.getJobId(), user.getLoginName(), "describe", "describe_image");

            log.info("Midjourney describe success, jobId: {}", responseData.getJobId());
            return responseData;

        } catch (IOException e) {
            log.error("Midjourney describe error", e);
            throw new LogicException(LogicErrorCode.MIDJOURNEY_API_ERROR);
        } catch (Exception e) {
            log.error("Midjourney describe error", e);
            throw new LogicException(LogicErrorCode.MIDJOURNEY_API_ERROR);
        }
    }

    /**
     * 查询任务状态
     */
    public MidjourneyResponse.TaskStatusResponse getTaskStatus(String jobId) {
        try {
            // 构建请求
            MidjourneyRequest.FetchRequest request = new MidjourneyRequest.FetchRequest();
            request.setJobId(jobId);

            // 调用API
            Call<MidjourneyResponse.TaskStatusResponse> call = midjourneyApiClient.fetch(midjourneyConfig.getApiKey(), request);
            Response<MidjourneyResponse.TaskStatusResponse> response = call.execute();

            if (!response.isSuccessful()) {
                log.error("Midjourney fetch API call failed: {}", response.errorBody().string());
                throw new LogicException(LogicErrorCode.MIDJOURNEY_API_ERROR);
            }

            MidjourneyResponse.TaskStatusResponse taskStatus = response.body();
            log.info("Midjourney task status: {}", JsonUtils.writeToString(taskStatus));

            return taskStatus;

        } catch (IOException e) {
            log.error("Midjourney fetch error", e);
            throw new LogicException(LogicErrorCode.MIDJOURNEY_API_ERROR);
        } catch (Exception e) {
            log.error("Midjourney fetch error", e);
            throw new LogicException(LogicErrorCode.MIDJOURNEY_API_ERROR);
        }
    }

    /**
     * Prompt效验 - 检查prompt是否包含被禁止的词汇
     */
    public MidjourneyResponse.PromptCheckResponse promptCheck(String prompt) {
        try {
            // 构建请求
            MidjourneyRequest.PromptCheckRequest request = new MidjourneyRequest.PromptCheckRequest();
            request.setPrompt(prompt);

            log.info("Midjourney prompt check request: {}", JsonUtils.writeToString(request));

            // 调用API
            Call<MidjourneyResponse.PromptCheckResponse> call = midjourneyApiClient.promptCheck(midjourneyConfig.getApiKey(), request);
            Response<MidjourneyResponse.PromptCheckResponse> response = call.execute();

            if (!response.isSuccessful()) {
                String errorBody = response.errorBody() != null ? response.errorBody().string() : "Unknown error";
                log.error("Midjourney prompt check API call failed with status {}: {}", response.code(), errorBody);
                throw new LogicException(LogicErrorCode.MIDJOURNEY_PROMPT_CHECK_FAILED);
            }

            MidjourneyResponse.PromptCheckResponse promptCheckResponse = response.body();
            log.info("Midjourney prompt check result: {}", JsonUtils.writeToString(promptCheckResponse));

            return promptCheckResponse;

        } catch (IOException e) {
            log.error("Midjourney prompt check error", e);
            throw new LogicException(LogicErrorCode.MIDJOURNEY_PROMPT_CHECK_FAILED);
        } catch (LogicException e) {
            // 重新抛出业务异常，保留原始错误信息
            throw e;
        } catch (Exception e) {
            log.error("Midjourney prompt check error", e);
            throw new LogicException(LogicErrorCode.MIDJOURNEY_PROMPT_CHECK_FAILED);
        }
    }

    /**
     * 保存PromptRecord到数据库
     */
    private void savePromptRecord(String jobId, String prompt, String mode, User user, String markId,
                                  Boolean fastHour, String platform, GenGenericPara genParameters) {
        try {
            PromptRecord promptRecord = new PromptRecord();
            promptRecord.setLoginName(user.getLoginName());
            promptRecord.setPromptId(jobId);
            promptRecord.setPrompt(prompt);
            promptRecord.setNegativePrompt(""); // Midjourney的负面提示词通过--no参数处理
            promptRecord.setCreateBy(user.getLoginName());
            promptRecord.setCreateTime(LocalDateTime.now());
            promptRecord.setOriginCreate(OriginCreate.midjourneyImagine.getValue());
            promptRecord.setBatchSize(4); // Midjourney每次生成4张图
            promptRecord.setModelId("midjourney"); // 使用固定的模型ID
            promptRecord.setMarkId(markId);
            promptRecord.setGenMode(mode);
            promptRecord.setFastHour(fastHour != null ? fastHour : Boolean.FALSE);
            promptRecord.setPlatform(platform);

            // 如果有原始参数，保存为genInfo
            if (genParameters != null) {
                promptRecord.setGenInfo(JsonUtils.writeToJsonNode(genParameters));
            }

            // 设置宽高比，根据前端SHAPE_ALL配置处理
            String aspectRatio = "1024 x 1024"; // 默认值
            if (genParameters instanceof GenGenericPara) {
                GenGenericPara genPara = (GenGenericPara) genParameters;
                aspectRatio = AspectRatioUtils.getAspectRatio(genPara);
            }
            promptRecord.setAspectRatio(aspectRatio);

            promptRecordMapper.insert(promptRecord);
            log.info("Saved PromptRecord for Midjourney task, jobId: {}, markId: {}", jobId, markId);

        } catch (Exception e) {
            log.error("Save PromptRecord error for jobId: {}", jobId, e);
        }
    }

    /**
     * 保存任务信息到Redis
     */
    private void saveTaskToRedis(String jobId, String loginName, String action, String prompt) {
        try {
            // 保存任务基本信息
            String taskKey = MIDJOURNEY_TASK_PREFIX + jobId;
            redisService.putDataToHash(taskKey, "loginName", loginName);
            redisService.putDataToHash(taskKey, "action", action);
            redisService.putDataToHash(taskKey, "prompt", prompt);
            redisService.putDataToHash(taskKey, "createTime", System.currentTimeMillis());
            redisService.expire(taskKey, 2, TimeUnit.HOURS);

            // 保存用户任务列表
            String userTaskKey = MIDJOURNEY_USER_TASK_PREFIX + loginName;
            redisService.putDataToHash(userTaskKey, jobId, System.currentTimeMillis());
            redisService.expire(userTaskKey, 2, TimeUnit.HOURS);

        } catch (Exception e) {
            log.error("Save task to redis error", e);
        }
    }

    /**
     * 检查用户Midjourney并发任务数是否超过限制
     *
     * @param user  用户信息
     * @param jobId 任务ID（用于预占位）
     * @return true表示超过限制，false表示可以继续
     */
    public boolean checkMidjourneyConcurrentJobs(User user, String jobId) {
        String lockKey = LockPrefixConstant.CONCURRENT_EXECUTION_LOCK_PREFIX + "ttapi:" + user.getId();
        RLock lock = redissonClient.getLock(lockKey);

        try {
            lock.lock();

            // 获取用户当前的Midjourney任务列表
            String userConcurrentKey = MIDJOURNEY_USER_CONCURRENT_PREFIX + user.getLoginName();
            List<String> userTaskList = redisService.getAllKeysFromHash(userConcurrentKey);

            // 清理已过期的任务
            cleanExpiredMidjourneyTasks(userConcurrentKey, userTaskList);

            // 重新获取清理后的任务列表
            userTaskList = redisService.getAllKeysFromHash(userConcurrentKey);

            // 检查是否超过最大并发数限制（默认10个）
            int maxConcurrentJobs = midjourneyConfig.getMaxConcurrentJobs() != null ?
                    midjourneyConfig.getMaxConcurrentJobs() : 10;

            if (userTaskList.size() >= maxConcurrentJobs) {
                log.warn("User {} Midjourney concurrent jobs limit exceeded: {}/{}",
                        user.getLoginName(), userTaskList.size(), maxConcurrentJobs);
                return true; // 超过限制
            }

            // 预占位：添加当前任务到并发列表
            if (jobId != null) {
                redisService.putDataToHash(userConcurrentKey, jobId, System.currentTimeMillis(), 2, TimeUnit.HOURS);
            }

            log.info("User {} Midjourney concurrent jobs check passed: {}/{}",
                    user.getLoginName(), userTaskList.size() + 1, maxConcurrentJobs);
            return false; // 未超过限制

        } catch (Exception e) {
            log.error("Check Midjourney concurrent jobs error for user: {}", user.getLoginName(), e);
            return true; // 出错时保守处理，拒绝请求
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    /**
     * 清理已过期的Midjourney任务
     */
    private void cleanExpiredMidjourneyTasks(String userConcurrentKey, List<String> taskList) {
        if (taskList == null || taskList.isEmpty()) {
            return;
        }

        try {
            for (String jobId : taskList) {
                String taskKey = MIDJOURNEY_TASK_PREFIX + jobId;

                // 检查任务是否还存在（TTL检查）
                Long expireTime = redisService.getExpireTime(taskKey, TimeUnit.SECONDS);
                if (expireTime == null || expireTime <= 0) {
                    // 任务已过期，从并发列表中移除
                    redisService.deleteFieldFromHash(userConcurrentKey, jobId);
                    log.debug("Removed expired Midjourney task: {}", jobId);
                }
            }
        } catch (Exception e) {
            log.error("Clean expired Midjourney tasks error", e);
        }
    }

    /**
     * 移除用户的Midjourney并发任务（任务完成时调用）
     */
    public void removeMidjourneyConcurrentJob(String loginName, String jobId) {
        try {
            String userConcurrentKey = MIDJOURNEY_USER_CONCURRENT_PREFIX + loginName;
            redisService.deleteFieldFromHash(userConcurrentKey, jobId);
            log.debug("Removed Midjourney concurrent job: {} for user: {}", jobId, loginName);
        } catch (Exception e) {
            log.error("Remove Midjourney concurrent job error", e);
        }
    }

    /**
     * 启动任务状态轮询
     * 每2秒查询一次任务状态，直到任务完成或失败
     */
    @Async
    public void startTaskStatusPolling(String jobId, String loginName) {
        log.info("Starting task status polling for jobId: {}, user: {}", jobId, loginName);

        CompletableFuture.runAsync(() -> {
            int maxAttempts = midjourneyConfig.getMaxPollingAttempts() != null ?
                    midjourneyConfig.getMaxPollingAttempts() : 150;
            int pollingInterval = midjourneyConfig.getPollingIntervalSeconds() != null ?
                    midjourneyConfig.getPollingIntervalSeconds() * 1000 : 2000; // 转换为毫秒
            int attempts = 0;

            while (attempts < maxAttempts) {
                try {
                    // 等待指定间隔
                    Thread.sleep(pollingInterval);
                    attempts++;

                    // 查询任务状态
                    MidjourneyResponse.TaskStatusResponse taskStatus = getTaskStatus(jobId);

                    if (taskStatus == null) {
                        log.warn("Failed to get task status for jobId: {}, attempt: {}", jobId, attempts);
                        continue;
                    }

                    String status = taskStatus.getStatus();
                    log.debug("Task status polling - jobId: {}, status: {}, attempt: {}", jobId, status, attempts);

                    MidjourneyTaskStatus taskStatusEnum = MidjourneyTaskStatus.fromStatus(status);

                    // 根据状态处理
                    switch (taskStatusEnum) {
                        case SUCCESS:
                            log.info("Task completed successfully - jobId: {}", jobId);
                            handleTaskSuccess(jobId, loginName, taskStatus);
                            return; // 任务完成，退出轮询

                        case FAILED:
                            log.warn("Task failed - jobId: {}", jobId);
                            handleTaskFailure(jobId, loginName, taskStatus);
                            return; // 任务失败，退出轮询

                        case PENDING_QUEUE:
                        case ON_QUEUE:
                            // 任务还在进行中，继续轮询
                            log.debug("Task still in progress - jobId: {}, status: {}", jobId, status);
                            break;

                        default:
                            log.warn("Unknown task status: {} for jobId: {}", status, jobId);
                            break;
                    }

                } catch (InterruptedException e) {
                    log.warn("Task status polling interrupted for jobId: {}", jobId);
                    Thread.currentThread().interrupt();
                    return;
                } catch (Exception e) {
                    log.error("Error during task status polling for jobId: {}, attempt: {}", jobId, attempts, e);
                }
            }

            // 超时处理
            log.warn("Task status polling timeout for jobId: {} after {} attempts", jobId, maxAttempts);
            handleTaskTimeout(jobId, loginName);
        });
    }

    /**
     * 处理任务成功
     */
    private void handleTaskSuccess(String jobId, String loginName, MidjourneyResponse.TaskStatusResponse taskStatus) {
        try {
            MidjourneyResponse.TaskData taskData = taskStatus.getData();
            if (taskData == null) {
                log.warn("Task data is null for successful jobId: {}", jobId);
                return;
            }

            // 更新PromptRecord的结束时间
            updatePromptRecordEndTime(jobId, loginName);

            // 处理图像数据
            if (!CollectionUtils.isEmpty(taskData.getImages())) {
                processTaskImages(jobId, loginName, taskData);
            }

            // 清理Redis任务信息
            cleanupTaskFromRedis(jobId, loginName);

            log.info("Successfully processed task completion for jobId: {}", jobId);

        } catch (Exception e) {
            log.error("Error handling task success for jobId: " + jobId, e);
        }
    }

    /**
     * 处理任务失败
     */
    private void handleTaskFailure(String jobId, String loginName, MidjourneyResponse.TaskStatusResponse taskStatus) {
        try {
            // 更新任务状态为失败
            LambdaUpdateWrapper<PromptRecord> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(PromptRecord::getPromptId, jobId)
                    .eq(PromptRecord::getLoginName, loginName)
                    .set(PromptRecord::getDel, true)
                    .set(PromptRecord::getUpdateTime, LocalDateTime.now());

            promptRecordMapper.update(null, updateWrapper);

            // 清理Redis任务信息
            cleanupTaskFromRedis(jobId, loginName);

            log.info("Processed failed task for jobId: {}", jobId);

        } catch (Exception e) {
            log.error("Error handling task failure for jobId: " + jobId, e);
        }
    }

    /**
     * 处理任务超时
     */
    private void handleTaskTimeout(String jobId, String loginName) {
        try {
            // 将超时任务标记为失败
            LambdaUpdateWrapper<PromptRecord> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(PromptRecord::getPromptId, jobId)
                    .eq(PromptRecord::getLoginName, loginName)
                    .set(PromptRecord::getDel, true)
                    .set(PromptRecord::getUpdateTime, LocalDateTime.now());

            promptRecordMapper.update(null, updateWrapper);

            // 清理Redis任务信息
            cleanupTaskFromRedis(jobId, loginName);

            log.warn("Processed timeout task for jobId: {}", jobId);

        } catch (Exception e) {
            log.error("Error handling task timeout for jobId: " + jobId, e);
        }
    }

    /**
     * 更新PromptRecord的结束时间
     */
    private void updatePromptRecordEndTime(String jobId, String loginName) {
        LambdaUpdateWrapper<PromptRecord> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(PromptRecord::getPromptId, jobId)
                .eq(PromptRecord::getLoginName, loginName)
                .set(PromptRecord::getGenEndTime, LocalDateTime.now())
                .set(PromptRecord::getUpdateTime, LocalDateTime.now());

        promptRecordMapper.update(null, updateWrapper);
    }

    /**
     * 处理任务图像数据
     */
    private void processTaskImages(String jobId, String loginName, MidjourneyResponse.TaskData taskData) {
        List<String> images = taskData.getImages();

        // 更新用户当日生图数量
        Integer userTodayCreateImgNums = (Integer) redisService.getDataFromHash(USER_TODAY_CREATE_IMG_NUMS, loginName);
        userTodayCreateImgNums = userTodayCreateImgNums != null ? userTodayCreateImgNums : 0;
        redisService.putDataToHash(USER_TODAY_CREATE_IMG_NUMS, loginName, userTodayCreateImgNums + images.size());

        // 保存图像文件信息到gpt_prompt_file表
        for (String imageUrl : images) {
            PromptFile promptFile = new PromptFile();
            promptFile.setLoginName(loginName);
            promptFile.setPromptId(jobId);

            // 从URL中提取文件名
            String fileName = extractFileNameFromUrl(imageUrl);
            promptFile.setFileName(fileName);

            // Midjourney返回的就是处理好的图片URL
            promptFile.setFileUrl(imageUrl);
            promptFile.setThumbnailUrl(imageUrl); // 使用同一个URL作为缩略图
            promptFile.setHighThumbnailUrl(imageUrl);

            // 设置图片尺寸
            if (taskData.getWidth() != null && taskData.getHeight() != null) {
                promptFile.setWidth(taskData.getWidth());
                promptFile.setHeight(taskData.getHeight());
            } else {
                // 默认Midjourney图片尺寸
                promptFile.setWidth(1024);
                promptFile.setHeight(1024);
            }

            promptFile.setCreateTime(LocalDateTime.now());
            promptFile.setCreateBy(loginName);

            promptFileMapper.insert(promptFile);
            log.info("Saved image file for jobId: {}, imageUrl: {}", jobId, imageUrl);
        }

        // 更新用户总图片数量
        LambdaUpdateWrapper<User> userUpdateWrapper = new LambdaUpdateWrapper<>();
        userUpdateWrapper.eq(User::getLoginName, loginName)
                .setSql("total_img_num = total_img_num + " + images.size());

        userMapper.update(null, userUpdateWrapper);

        // 更新VIP相关统计，参考GenService的callBack方法
        try {
            vipService.updateMessageByMarkId(jobId, jobId, loginName, images.size(), images.size(), images.size());
        } catch (Exception e) {
            log.error("更新VIP统计信息失败，jobId: {}, user: {}", jobId, loginName, e);
        }

        log.info("Processed {} images for jobId: {}, user: {}", images.size(), jobId, loginName);
    }

    /**
     * 从URL中提取文件名
     */
    private String extractFileNameFromUrl(String imageUrl) {
        if (StringUtil.isBlank(imageUrl)) {
            return "midjourney_image.png";
        }

        try {
            String[] parts = imageUrl.split("/");
            String lastPart = parts[parts.length - 1];

            // 移除查询参数
            if (lastPart.contains("?")) {
                lastPart = lastPart.substring(0, lastPart.indexOf("?"));
            }

            return StringUtil.isNotBlank(lastPart) ? lastPart : "midjourney_image.png";
        } catch (Exception e) {
            log.warn("Failed to extract filename from URL: {}", imageUrl);
            return "midjourney_image.png";
        }
    }

    /**
     * 清理Redis任务信息
     */
    private void cleanupTaskFromRedis(String jobId, String loginName) {
        try {
            // 删除任务信息
            String taskKey = MIDJOURNEY_TASK_PREFIX + jobId;
            redisService.delete(taskKey);

            // 从用户任务列表中删除
            String userTaskKey = MIDJOURNEY_USER_TASK_PREFIX + loginName;
            redisService.deleteFieldFromHash(userTaskKey, jobId);

            // 从并发任务列表中删除
            removeMidjourneyConcurrentJob(loginName, jobId);

        } catch (Exception e) {
            log.error("Error cleaning up task from redis for jobId: " + jobId, e);
        }
    }
}
