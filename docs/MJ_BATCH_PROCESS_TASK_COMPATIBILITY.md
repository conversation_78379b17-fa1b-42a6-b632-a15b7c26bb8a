# Midjourney API 兼容性实现 - batchProcessTask

## 概述

本文档描述了如何为 `batchProcessTask` 方法添加 Midjourney API 兼容性，使其能够同时处理传统的 `markId` 和 Midjourney 的 `jobId`。

## 背景

原有系统中：
- 传统生图使用 `markId` 进行任务跟踪
- 前端通过 `batchProcessTask` 轮询任务状态
- Midjourney API 使用 `jobId` 进行任务跟踪
- 需要统一接口支持两种任务类型

## 实现方案

### 1. 核心修改

#### TaskService.java 主要变更：

1. **processTask 方法重构**
   ```java
   public TaskProcessMessage processTask(String taskId) throws JsonProcessingException {
       // 检查是否为MJ任务
       if (isMidjourneyTask(taskId)) {
           return processMidjourneyTask(taskId);
       }
       
       // 处理传统markId任务
       return processTraditionalTask(taskId);
   }
   ```

2. **任务类型检测**
   ```java
   private boolean isMidjourneyTask(String taskId) {
       // 通过查询数据库中的 PromptRecord 来判断是否为MJ任务
       // 检查 originCreate 字段是否为 MJ 相关值
   }
   ```

3. **MJ任务处理**
   ```java
   private TaskProcessMessage processMidjourneyTask(String jobId) {
       // 调用 MidjourneyService.getTaskStatus() 获取实时状态
       // 转换MJ状态到系统统一状态格式
       // 处理成功任务的图片数据
   }
   ```

### 2. 状态映射

| MJ状态 | 系统状态 | 描述 |
|--------|----------|------|
| PENDING_QUEUE | pending | 排队中 |
| ON_QUEUE | running | 执行中 |
| SUCCESS | success | 成功完成 |
| FAILED | failure | 失败 |

### 3. 数据流程

```
前端请求 batchProcessTask([markId1, jobId1, markId2])
    ↓
TaskService.batchProcessTask()
    ↓
对每个ID调用 processTask()
    ↓
isMidjourneyTask() 判断任务类型
    ↓
分别调用 processTraditionalTask() 或 processMidjourneyTask()
    ↓
返回统一格式的 TaskProcessMessage
```

## 关键特性

### 1. 向后兼容
- 完全保持对传统 `markId` 的支持
- 不影响现有功能

### 2. 统一接口
- 前端无需区分任务类型
- 可以在同一个请求中混合查询不同类型的任务

### 3. 实时状态
- MJ任务通过API实时查询状态
- 传统任务保持原有逻辑

### 4. 错误处理
- 优雅处理API调用失败
- 提供详细的错误信息

## 使用示例

### 前端调用示例
```javascript
// 混合查询传统markId和MJ jobId
const taskIds = [
    "1234567890123456789",  // 传统markId
    "mj-job-uuid-12345",    // MJ jobId
    "9876543210987654321"   // 传统markId
];

fetch('/api/task/batch-process-task', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(taskIds)
})
.then(response => response.json())
.then(data => {
    data.data.forEach(task => {
        console.log(`任务 ${task.markId}: ${task.status}`);
        if (task.status === 'success' && task.img_urls) {
            console.log(`生成了 ${task.img_urls.length} 张图片`);
        }
    });
});
```

### 返回数据格式
```json
{
    "code": 200,
    "data": [
        {
            "markId": "1234567890123456789",
            "promptId": "prompt-id-123",
            "status": "success",
            "prompt": "a beautiful landscape",
            "featureName": "create",
            "img_urls": [
                {
                    "imgUrl": "https://example.com/image1.jpg",
                    "thumbnailUrl": "https://example.com/thumb1.jpg",
                    "realWidth": 1024,
                    "realHeight": 1024
                }
            ]
        },
        {
            "markId": "generated-mark-id",
            "promptId": "mj-job-uuid-12345",
            "status": "running",
            "prompt": "a cute cat",
            "featureName": "midjourneyImagine",
            "index": 0
        }
    ]
}
```

## 测试

### 单元测试
- `TaskServiceMjCompatibilityTest.java` 提供了完整的测试用例
- 测试混合ID处理、单独MJ任务处理、传统任务兼容性

### 集成测试
建议在实际环境中测试：
1. 创建MJ任务并获取jobId
2. 使用batchProcessTask查询状态
3. 验证状态转换和数据完整性

## 注意事项

1. **性能考虑**
   - MJ任务需要调用外部API，可能有延迟
   - 建议合理控制批量查询的数量

2. **错误处理**
   - MJ API调用失败时会返回failure状态
   - 网络问题不会影响传统任务查询

3. **数据一致性**
   - MJ任务的markId通过PromptRecord关联
   - 确保数据库中的记录完整性

## 扩展性

该实现为未来支持更多第三方API提供了良好的架构基础：
- 可以轻松添加新的任务类型检测
- 统一的状态转换机制
- 模块化的处理逻辑

## 总结

通过这次实现，系统现在能够：
- 无缝支持MJ API的jobId查询
- 保持对传统markId的完全兼容
- 提供统一的前端接口
- 实现实时状态同步

这为用户提供了更好的体验，同时为系统的未来扩展奠定了基础。
